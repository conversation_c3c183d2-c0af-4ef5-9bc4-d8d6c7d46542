import * as vscode from 'vscode';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { io, Socket } from 'socket.io-client';

/**
 * Response interface for API calls
 */
interface ApiResponse {
    status: string;
    error?: string;
    session_id?: string;
    message?: string;
    [key: string]: any;
}

/**
 * Session configuration interface
 */
interface SessionConfig {
    problem_statement: string;
    repo_path: string;
    model_name: string;
    tools?: string[];
    environment?: Record<string, any>;
}

/**
 * Client for communicating with the AI Coding Agent bridge
 */
export class BridgeClient {
    private outputChannel: vscode.OutputChannel;
    private host: string;
    private port: number;
    private baseUrl: string;
    private statusCallback: (status: 'connected' | 'disconnected' | 'running' | 'error') => void;
    private httpClient: AxiosInstance;
    private socket: Socket | null = null;
    private currentSessionId: string | null = null;

    /**
     * Create a new BridgeClient
     *
     * @param outputChannel VS Code output channel for logging
     * @param statusCallback Callback function for status updates
     */
    constructor(
        outputChannel: vscode.OutputChannel,
        statusCallback: (status: 'connected' | 'disconnected' | 'running' | 'error') => void
    ) {
        const config = vscode.workspace.getConfiguration('aiCodingAgent');
        this.host = config.get<string>('bridgeHost') || 'localhost';
        this.port = config.get<number>('bridgePort') || 8080;
        this.baseUrl = `http://${this.host}:${this.port}`;
        this.outputChannel = outputChannel;
        this.statusCallback = statusCallback;

        // Initialize HTTP client with timeout and error handling
        this.httpClient = axios.create({
            baseURL: this.baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json'
            }
        });

        // Add response interceptor for error handling
        this.httpClient.interceptors.response.use(
            (response: AxiosResponse) => response,
            (error) => {
                this.outputChannel.appendLine(`HTTP Error: ${error.message}`);
                if (error.response) {
                    this.outputChannel.appendLine(`Status: ${error.response.status}`);
                    this.outputChannel.appendLine(`Data: ${JSON.stringify(error.response.data)}`);
                }
                return Promise.reject(error);
            }
        );
    }

    /**
     * Connect to the bridge
     */
    public async connect(): Promise<void> {
        this.outputChannel.appendLine(`Connecting to AI Coding Agent bridge at ${this.baseUrl}...`);

        try {
            // Check if bridge is available
            await this.checkStatus();

            // Initialize WebSocket connection
            await this.initializeWebSocket();

            this.outputChannel.appendLine('Successfully connected to AI Coding Agent bridge');
            this.statusCallback('connected');
        } catch (error: any) {
            this.outputChannel.appendLine(`Failed to connect to AI Coding Agent bridge: ${error.message}`);
            this.statusCallback('disconnected');
        }
    }

    /**
     * Disconnect from the bridge
     */
    public disconnect(): void {
        this.outputChannel.appendLine('Disconnecting from AI Coding Agent bridge');

        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }

        this.currentSessionId = null;
        this.statusCallback('disconnected');
    }

    /**
     * Initialize WebSocket connection for real-time updates
     */
    private async initializeWebSocket(): Promise<void> {
        return new Promise((resolve, reject) => {
            try {
                this.socket = io(this.baseUrl, {
                    transports: ['websocket', 'polling'],
                    timeout: 10000
                });

                this.socket.on('connect', () => {
                    this.outputChannel.appendLine('WebSocket connected');
                    resolve();
                });

                this.socket.on('disconnect', () => {
                    this.outputChannel.appendLine('WebSocket disconnected');
                    this.statusCallback('disconnected');
                });

                this.socket.on('connect_error', (error: any) => {
                    this.outputChannel.appendLine(`WebSocket connection error: ${error.message}`);
                    reject(error);
                });

                this.socket.on('session_event', (data: any) => {
                    this.handleSessionEvent(data);
                });

                this.socket.on('trajectory_updated', (data: any) => {
                    this.handleTrajectoryUpdate(data);
                });

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Handle session events from WebSocket
     */
    private handleSessionEvent(data: any): void {
        this.outputChannel.appendLine(`Session Event: ${JSON.stringify(data)}`);

        if (data.session_id === this.currentSessionId) {
            switch (data.event_type) {
                case 'session_started':
                    this.statusCallback('running');
                    break;
                case 'session_completed':
                    this.statusCallback('connected');
                    this.currentSessionId = null;
                    break;
                case 'session_failed':
                    this.statusCallback('error');
                    this.currentSessionId = null;
                    break;
            }
        }
    }

    /**
     * Handle trajectory updates from WebSocket
     */
    private handleTrajectoryUpdate(data: any): void {
        this.outputChannel.appendLine(`Trajectory Update: ${JSON.stringify(data)}`);
    }

    /**
     * Run the AI Coding Agent
     *
     * @param problemStatement Problem statement to solve
     * @param repoPath Path to the repository
     * @param modelName Name of the model to use
     * @returns Promise with the result
     */
    public async runAgent(problemStatement: string, repoPath: string, modelName: string): Promise<ApiResponse> {
        this.outputChannel.appendLine(`Running AI Coding Agent with task: ${problemStatement}`);
        this.outputChannel.appendLine(`Repository: ${repoPath}`);
        this.outputChannel.appendLine(`Model: ${modelName}`);

        try {
            const sessionConfig: SessionConfig = {
                problem_statement: problemStatement,
                repo_path: repoPath,
                model_name: modelName
            };

            // Create a new session
            const response = await this.httpClient.post<ApiResponse>('/api/sessions', sessionConfig);

            if (response.data.status === 'created' && response.data.session_id) {
                this.currentSessionId = response.data.session_id;
                this.outputChannel.appendLine(`Created session: ${this.currentSessionId}`);

                // Start the session
                const startResponse = await this.httpClient.post<ApiResponse>(
                    `/api/sessions/${this.currentSessionId}/start`
                );

                if (startResponse.data.status === 'success') {
                    this.statusCallback('running');
                    this.outputChannel.appendLine('AI Coding Agent started successfully');

                    // Subscribe to session updates via WebSocket
                    if (this.socket) {
                        this.socket.emit('subscribe_session', { session_id: this.currentSessionId });
                    }

                    return { status: 'success', session_id: this.currentSessionId };
                } else {
                    throw new Error(startResponse.data.error || 'Failed to start session');
                }
            } else {
                throw new Error(response.data.error || 'Failed to create session');
            }

        } catch (error: any) {
            this.outputChannel.appendLine(`Error running AI Coding Agent: ${error.message}`);
            this.statusCallback('error');

            if (error.response?.data?.error) {
                throw new Error(error.response.data.error);
            } else {
                throw error;
            }
        }
    }

    /**
     * Stop the AI Coding Agent
     *
     * @returns Promise with the result
     */
    public async stopAgent(): Promise<ApiResponse> {
        this.outputChannel.appendLine('Stopping AI Coding Agent...');

        try {
            if (!this.currentSessionId) {
                return { status: 'success', message: 'No active session to stop' };
            }

            const response = await this.httpClient.post<ApiResponse>(
                `/api/sessions/${this.currentSessionId}/stop`
            );

            if (response.data.status === 'success') {
                this.outputChannel.appendLine('AI Coding Agent stopped successfully');
                this.statusCallback('connected');
                this.currentSessionId = null;
                return response.data;
            } else {
                throw new Error(response.data.error || 'Failed to stop session');
            }

        } catch (error: any) {
            this.outputChannel.appendLine(`Error stopping AI Coding Agent: ${error.message}`);
            this.statusCallback('error');

            if (error.response?.data?.error) {
                throw new Error(error.response.data.error);
            } else {
                throw error;
            }
        }
    }

    /**
     * Check the status of the AI Coding Agent bridge
     *
     * @returns Promise with the result
     */
    public async checkStatus(): Promise<ApiResponse> {
        this.outputChannel.appendLine('Checking AI Coding Agent bridge status...');

        try {
            const response = await this.httpClient.get<ApiResponse>('/health');

            if (response.data.status === 'ok') {
                this.outputChannel.appendLine('AI Coding Agent bridge is healthy');
                return response.data;
            } else {
                throw new Error('Bridge health check failed');
            }

        } catch (error: any) {
            this.outputChannel.appendLine(`Bridge health check failed: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get current session information
     *
     * @returns Current session ID or null
     */
    public getCurrentSessionId(): string | null {
        return this.currentSessionId;
    }

    /**
     * Get session details
     *
     * @param sessionId Session ID to get details for
     * @returns Promise with session details
     */
    public async getSessionDetails(sessionId?: string): Promise<ApiResponse> {
        const id = sessionId || this.currentSessionId;
        if (!id) {
            throw new Error('No session ID provided');
        }

        try {
            const response = await this.httpClient.get<ApiResponse>(`/api/sessions/${id}`);
            return response.data;
        } catch (error: any) {
            this.outputChannel.appendLine(`Error getting session details: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get session trajectory
     *
     * @param sessionId Session ID to get trajectory for
     * @returns Promise with trajectory data
     */
    public async getSessionTrajectory(sessionId?: string): Promise<ApiResponse> {
        const id = sessionId || this.currentSessionId;
        if (!id) {
            throw new Error('No session ID provided');
        }

        try {
            const response = await this.httpClient.get<ApiResponse>(`/api/sessions/${id}/trajectory`);
            return response.data;
        } catch (error: any) {
            this.outputChannel.appendLine(`Error getting session trajectory: ${error.message}`);
            throw error;
        }
    }
}
