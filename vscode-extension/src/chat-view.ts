import * as vscode from 'vscode';
import { BridgeClient } from './bridge-client';

/**
 * Chat View for interacting with the AI Coding Agent
 */
export class ChatView {
    private panel: vscode.WebviewPanel | undefined;
    private bridgeClient: BridgeClient;
    private context: vscode.ExtensionContext;
    private messages: Array<{ role: string; content: string }> = [];
    private currentChatSessionId: string | null = null;

    /**
     * Create a new ChatView
     * 
     * @param context Extension context
     * @param bridgeClient Bridge client for communicating with the AI Coding Agent
     */
    constructor(context: vscode.ExtensionContext, bridgeClient: BridgeClient) {
        this.context = context;
        this.bridgeClient = bridgeClient;
    }

    /**
     * Show the chat view
     */
    public show() {
        if (this.panel) {
            this.panel.reveal(vscode.ViewColumn.Beside);
            return;
        }

        // Create and show the panel
        this.panel = vscode.window.createWebviewPanel(
            'aiCodingAgentChat',
            'AI Coding Agent Chat',
            vscode.ViewColumn.Beside,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(this.context.extensionUri, 'media')
                ]
            }
        );

        // Set the HTML content
        this.updateWebview();

        // Handle messages from the webview
        this.panel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.command) {
                    case 'sendMessage':
                        await this.sendMessage(message.text);
                        break;
                    case 'stopAgent':
                        await this.stopAgent();
                        break;
                }
            },
            undefined,
            this.context.subscriptions
        );

        // Reset when the panel is disposed
        this.panel.onDidDispose(
            () => {
                this.panel = undefined;
            },
            null,
            this.context.subscriptions
        );
    }

    /**
     * Send a message to the AI Coding Agent
     *
     * @param text Message text
     */
    private async sendMessage(text: string) {
        if (!this.panel) {
            return;
        }

        // Add user message to the chat
        this.messages.push({ role: 'user', content: text });
        this.updateWebview();

        try {
            // Get the current workspace folder and file context
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders) {
                this.addSystemMessage('No workspace folder is open. Please open a folder first.');
                return;
            }

            // Get current file context
            const activeEditor = vscode.window.activeTextEditor;
            const context: any = {
                project_root: workspaceFolders[0].uri.fsPath
            };

            if (activeEditor) {
                context.file_path = activeEditor.document.uri.fsPath;
                context.language = activeEditor.document.languageId;

                // Get current selection if any
                const selection = activeEditor.selection;
                if (!selection.isEmpty) {
                    context.current_selection = activeEditor.document.getText(selection);
                    context.cursor_position = {
                        line: selection.start.line,
                        column: selection.start.character
                    };
                }
            }

            // Add thinking message
            this.addSystemMessage('Processing your request...');

            // Create or reuse chat session
            if (!this.currentChatSessionId) {
                const chatSession = await this.bridgeClient.createChatSession(
                    'VS Code Chat Session',
                    context
                );
                this.currentChatSessionId = chatSession.session_id;
                this.addSystemMessage(`Chat session created: ${this.currentChatSessionId}`);

                // Join the chat session for real-time updates
                this.bridgeClient.joinChatSession(
                    this.currentChatSessionId,
                    undefined, // onMessage
                    (data: any) => {
                        // onChunk - add streaming text
                        if (data.chunk) {
                            this.appendToLastAssistantMessage(data.chunk);
                        }
                    },
                    (data: any) => {
                        // onComplete - finalize the response
                        this.addSystemMessage('Response completed.');
                    },
                    (error: any) => {
                        // onError
                        this.addSystemMessage(`Error: ${error.error || error.message || 'Unknown error'}`);
                    }
                );
            }

            // Send message via WebSocket for real-time streaming
            this.bridgeClient.sendChatMessageStream(this.currentChatSessionId, text, context);

            // Start a new assistant message for streaming
            this.messages.push({ role: 'assistant', content: '' });
            this.updateWebview();

        } catch (error: any) {
            this.addSystemMessage(`Error: ${error.message}`);
        }
    }

    /**
     * Stop the AI Coding Agent
     */
    private async stopAgent() {
        try {
            const response = await this.bridgeClient.stopAgent();

            if (response.status === 'success') {
                this.addSystemMessage('AI Coding Agent stopped successfully');
            } else {
                this.addSystemMessage(`Failed to stop AI Coding Agent: ${response.error || 'Unknown error'}`);
            }
        } catch (error: any) {
            this.addSystemMessage(`Error stopping AI Coding Agent: ${error.message}`);
        }
    }

    /**
     * Add a system message to the chat
     * 
     * @param text Message text
     */
    private addSystemMessage(text: string) {
        this.messages.push({ role: 'system', content: text });
        this.updateWebview();
    }

    /**
     * Add an assistant message to the chat
     *
     * @param text Message text
     */
    public addAssistantMessage(text: string) {
        this.messages.push({ role: 'assistant', content: text });
        this.updateWebview();
    }

    /**
     * Append text to the last assistant message (for streaming)
     *
     * @param text Text to append
     */
    private appendToLastAssistantMessage(text: string) {
        if (this.messages.length > 0) {
            const lastMessage = this.messages[this.messages.length - 1];
            if (lastMessage.role === 'assistant') {
                lastMessage.content += text;
                this.updateWebview();
            }
        }
    }

    /**
     * Update the webview content
     */
    private updateWebview() {
        if (!this.panel) {
            return;
        }

        this.panel.webview.html = this.getHtmlForWebview();
    }

    /**
     * Get the HTML for the webview
     * 
     * @returns HTML content
     */
    private getHtmlForWebview(): string {
        const messagesHtml = this.messages.map(message => {
            const role = message.role === 'user' ? 'You' : message.role === 'assistant' ? 'AI Agent' : 'System';
            const roleClass = message.role;
            
            return `
                <div class="message ${roleClass}">
                    <div class="message-header">${role}</div>
                    <div class="message-content">${this.formatMessageContent(message.content)}</div>
                </div>
            `;
        }).join('');

        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AI Coding Agent Chat</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        font-size: var(--vscode-font-size);
                        color: var(--vscode-foreground);
                        padding: 0;
                        margin: 0;
                        display: flex;
                        flex-direction: column;
                        height: 100vh;
                    }
                    .chat-container {
                        flex: 1;
                        overflow-y: auto;
                        padding: 16px;
                    }
                    .message {
                        margin-bottom: 16px;
                        padding: 12px;
                        border-radius: 4px;
                    }
                    .message-header {
                        font-weight: bold;
                        margin-bottom: 8px;
                    }
                    .message-content {
                        white-space: pre-wrap;
                    }
                    .user {
                        background-color: var(--vscode-editor-inactiveSelectionBackground);
                    }
                    .assistant {
                        background-color: var(--vscode-editor-selectionBackground);
                    }
                    .system {
                        background-color: var(--vscode-editorWidget-background);
                        font-style: italic;
                    }
                    .input-container {
                        display: flex;
                        padding: 16px;
                        border-top: 1px solid var(--vscode-panel-border);
                    }
                    #message-input {
                        flex: 1;
                        padding: 8px;
                        background-color: var(--vscode-input-background);
                        color: var(--vscode-input-foreground);
                        border: 1px solid var(--vscode-input-border);
                        border-radius: 4px;
                    }
                    button {
                        margin-left: 8px;
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                    }
                    button:hover {
                        background-color: var(--vscode-button-hoverBackground);
                    }
                    pre {
                        background-color: var(--vscode-editor-background);
                        padding: 8px;
                        border-radius: 4px;
                        overflow-x: auto;
                    }
                    code {
                        font-family: var(--vscode-editor-font-family);
                        font-size: var(--vscode-editor-font-size);
                    }
                </style>
            </head>
            <body>
                <div class="chat-container">
                    ${messagesHtml}
                </div>
                <div class="input-container">
                    <textarea id="message-input" placeholder="Type your message..." rows="3"></textarea>
                    <button id="send-button">Send</button>
                    <button id="stop-button">Stop</button>
                </div>
                <script>
                    const vscode = acquireVsCodeApi();
                    const messageInput = document.getElementById('message-input');
                    const sendButton = document.getElementById('send-button');
                    const stopButton = document.getElementById('stop-button');
                    const chatContainer = document.querySelector('.chat-container');
                    
                    // Scroll to bottom
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                    
                    // Send message
                    function sendMessage() {
                        const text = messageInput.value.trim();
                        if (text) {
                            vscode.postMessage({
                                command: 'sendMessage',
                                text: text
                            });
                            messageInput.value = '';
                        }
                    }
                    
                    // Stop agent
                    function stopAgent() {
                        vscode.postMessage({
                            command: 'stopAgent'
                        });
                    }
                    
                    // Event listeners
                    sendButton.addEventListener('click', sendMessage);
                    stopButton.addEventListener('click', stopAgent);
                    
                    messageInput.addEventListener('keydown', (event) => {
                        if (event.key === 'Enter' && !event.shiftKey) {
                            event.preventDefault();
                            sendMessage();
                        }
                    });
                </script>
            </body>
            </html>
        `;
    }

    /**
     * Format message content with Markdown-like syntax
     *
     * @param content Message content
     * @returns Formatted HTML
     */
    private formatMessageContent(content: string): string {
        // Replace code blocks
        let formatted = content.replace(/```(\w*)([\s\S]*?)```/g, (_match, language, code) => {
            return `<pre><code class="language-${language}">${this.escapeHtml(code.trim())}</code></pre>`;
        });

        // Replace inline code
        formatted = formatted.replace(/`([^`]+)`/g, '<code>$1</code>');

        // Replace newlines with <br>
        formatted = formatted.replace(/\n/g, '<br>');

        return formatted;
    }

    /**
     * Escape HTML special characters
     * 
     * @param text Text to escape
     * @returns Escaped text
     */
    private escapeHtml(text: string): string {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }
}
