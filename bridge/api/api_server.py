"""
API server for the AI Coding Agent bridge.
"""

import logging
import os

from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_socketio import Socket<PERSON>, emit

from bridge.core.config import config
from bridge.integrations.swe_agent_interface import SWEAgentInterface
from bridge.api.completion_endpoints import completion_bp, setup_completion_websocket
from bridge.api.chat_endpoints import chat_bp, setup_chat_websocket
from bridge.api.auth_endpoints import auth_bp
from bridge.api.workspace_endpoints import workspace_bp
from bridge.auth.session_auth import session_auth

# Set up logging
logging_level = getattr(logging, config.get("logging", "level", default="INFO"))
log_file = config.get("logging", "file", default="logs/bridge.log")
os.makedirs(os.path.dirname(log_file), exist_ok=True)

logging.basicConfig(
    level=logging_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# Register blueprints
app.register_blueprint(completion_bp)
app.register_blueprint(chat_bp)
app.register_blueprint(auth_bp)
app.register_blueprint(workspace_bp)

# Set up WebSocket handlers
setup_completion_websocket(socketio)
setup_chat_websocket(socketio)

# Initialize SWE-Agent interface
swe_agent = SWEAgentInterface()


@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({"status": "ok"})


@app.route('/api/run', methods=['POST'])
def run_agent():
    """Run the SWE-Agent on a problem statement."""
    try:
        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400
        
        problem_statement = data.get('problem_statement')
        if not problem_statement:
            return jsonify({"error": "No problem statement provided"}), 400
        
        repo_path = data.get('repo_path')
        if not repo_path:
            return jsonify({"error": "No repository path provided"}), 400
        
        model_name = data.get('model_name', 'gpt-4')
        
        # Run the agent
        result = swe_agent.run_agent(
            problem_statement=problem_statement,
            repo_path=repo_path,
            model_name=model_name
        )
        
        return jsonify({"status": result})
    
    except Exception as e:
        logger.exception("Error running agent")
        return jsonify({"error": str(e)}), 500


@app.route('/api/stop', methods=['POST'])
def stop_agent():
    """Stop the current SWE-Agent run."""
    try:
        result = swe_agent.stop_agent()
        return jsonify({"status": "success" if result else "error"})
    
    except Exception as e:
        logger.exception("Error stopping agent")
        return jsonify({"error": str(e)}), 500


@app.route('/api/config', methods=['GET'])
def get_config():
    """Get the current configuration."""
    return jsonify(config.config)


# Enhanced Session management endpoints using SessionManager
@app.route('/api/sessions', methods=['POST'])
def create_session():
    """Create a new agent session using enhanced SessionManager."""
    try:
        # Import SessionManager for enhanced session management
        from bridge.core.session_manager import session_manager, SessionConfig

        data = request.json
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Validate required fields
        problem_statement = data.get('problem_statement')
        if not problem_statement:
            return jsonify({"error": "No problem statement provided"}), 400

        repo_path = data.get('repo_path')
        if not repo_path:
            return jsonify({"error": "No repository path provided"}), 400

        # Create session configuration
        config_data = SessionConfig(
            model_name=data.get('model_name', 'claude-3-opus-20240229'),
            repo_path=repo_path,
            problem_statement=problem_statement,
            tools=data.get('tools'),
            environment=data.get('environment'),
            retry_config=data.get('retry_config')
        )

        # Create session using SessionManager
        session_id = session_manager.create_session(config_data)

        return jsonify({
            "status": "created",
            "session_id": session_id
        }), 201

    except Exception as e:
        logger.exception("Error creating session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions', methods=['GET'])
def list_sessions():
    """List all sessions."""
    try:
        from bridge.core.session_manager import session_manager, SessionStatus

        status_filter = request.args.get('status')
        status_enum = None

        if status_filter:
            try:
                status_enum = SessionStatus(status_filter)
            except ValueError:
                return jsonify({"error": f"Invalid status: {status_filter}"}), 400

        sessions = session_manager.list_sessions(status_enum)
        return jsonify({
            "sessions": [session.to_dict() for session in sessions]
        })

    except Exception as e:
        logger.exception("Error listing sessions")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/start', methods=['POST'])
def start_session(session_id):
    """Start a session using SessionManager."""
    try:
        from bridge.core.session_manager import session_manager, SessionStatus

        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404

        # Update session status to running
        success = session_manager.update_session_status(session_id, SessionStatus.RUNNING)
        if not success:
            return jsonify({"error": "Failed to start session"}), 500

        # Initialize progress tracking
        session_manager.update_session_progress(session_id, 0.1, "Initializing agent")

        # Emit WebSocket event for real-time updates
        socketio.emit('session_event', {
            'session_id': session_id,
            'event_type': 'session_started',
            'message': 'Session started successfully'
        })

        return jsonify({"status": "success"})

    except Exception as e:
        logger.exception("Error starting session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/stop', methods=['POST'])
def stop_session(session_id):
    """Stop a session using SessionManager."""
    try:
        from bridge.core.session_manager import session_manager, SessionStatus

        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404

        # Update session status to terminated
        success = session_manager.update_session_status(session_id, SessionStatus.TERMINATED)
        if not success:
            return jsonify({"error": "Failed to stop session"}), 500

        # Emit WebSocket event for real-time updates
        socketio.emit('session_event', {
            'session_id': session_id,
            'event_type': 'session_stopped',
            'message': 'Session stopped successfully'
        })

        return jsonify({"status": "success"})

    except Exception as e:
        logger.exception("Error stopping session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>', methods=['GET'])
def get_session(session_id):
    """Get session details using SessionManager."""
    try:
        from bridge.core.session_manager import session_manager

        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404

        return jsonify(session.to_dict())

    except Exception as e:
        logger.exception("Error getting session")
        return jsonify({"error": str(e)}), 500


@app.route('/api/sessions/<session_id>/trajectory', methods=['GET'])
def get_session_trajectory(session_id):
    """Get session trajectory using SessionManager."""
    try:
        from bridge.core.session_manager import session_manager

        session = session_manager.get_session(session_id)
        if not session:
            return jsonify({"error": "Session not found"}), 404

        return jsonify({
            "session_id": session_id,
            "trajectory": session.trajectory,
            "status": "success"
        })

    except Exception as e:
        logger.exception("Error getting session trajectory")
        return jsonify({"error": str(e)}), 500


@socketio.on('connect')
def handle_connect():
    """Handle client connection."""
    logger.info("Client connected")


@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection."""
    logger.info("Client disconnected")


@socketio.on('subscribe_session')
def handle_subscribe_session(data):
    """Handle session subscription for real-time updates."""
    try:
        session_id = data.get('session_id')
        if not session_id:
            emit('error', {"error": "No session_id provided"})
            return

        # Join room for this session
        from flask_socketio import join_room
        join_room(session_id)

        logger.info(f"Client subscribed to session {session_id}")
        emit('subscribed', {
            "session_id": session_id,
            "message": "Subscribed to session updates"
        })

    except Exception as e:
        logger.error(f"Error in session subscription: {e}")
        emit('error', {"error": str(e)})


@socketio.on('unsubscribe_session')
def handle_unsubscribe_session(data):
    """Handle session unsubscription."""
    try:
        session_id = data.get('session_id')
        if not session_id:
            emit('error', {"error": "No session_id provided"})
            return

        # Leave room for this session
        from flask_socketio import leave_room
        leave_room(session_id)

        logger.info(f"Client unsubscribed from session {session_id}")
        emit('unsubscribed', {
            "session_id": session_id,
            "message": "Unsubscribed from session updates"
        })

    except Exception as e:
        logger.error(f"Error in session unsubscription: {e}")
        emit('error', {"error": str(e)})


def run_server(debug=False, threaded=True):
    """Run the API server."""
    host = config.get("bridge", "api_host", default="localhost")
    port = config.get("bridge", "api_port", default=8080)

    logger.info(f"Starting API server on {host}:{port}")

    # Use different settings based on whether we're in main thread or not
    import threading
    is_main_thread = threading.current_thread() is threading.main_thread()

    if is_main_thread:
        # Running in main thread - can use debug mode
        socketio.run(app, host=host, port=port, debug=debug, allow_unsafe_werkzeug=True)
    else:
        # Running in thread - disable debug mode to avoid signal issues
        socketio.run(app, host=host, port=port, debug=False, allow_unsafe_werkzeug=True, use_reloader=False)


if __name__ == "__main__":
    run_server()
