"""
Direct LLM client for chat functionality.
This provides a simple interface to call LLMs directly without the complex SWE-Agent setup.
"""

import asyncio
import logging
import os
from typing import AsyncGenerator, Dict, Any, Optional, List
import litellm
from bridge.core.config import config

logger = logging.getLogger(__name__)


class LLMClient:
    """Direct LLM client for chat responses."""
    
    def __init__(self):
        """Initialize the LLM client."""
        self.model_name = config.get("swe_agent", "model", default="claude-3-haiku-20240307")
        self.temperature = 0.1
        self.max_tokens = 4000
        
        # Set up API keys
        self._setup_api_keys()
        
        logger.info(f"LLM Client initialized with model: {self.model_name}")
    
    def _setup_api_keys(self):
        """Set up API keys from configuration."""
        anthropic_key = config.get("api_keys", "anthropic")
        openai_key = config.get("api_keys", "openai")
        
        if anthropic_key:
            os.environ["ANTHROPIC_API_KEY"] = anthropic_key
            logger.info("Anthropic API key configured")
        
        if openai_key:
            os.environ["OPENAI_API_KEY"] = openai_key
            logger.info("OpenAI API key configured")
    
    async def generate_response(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate a response using the LLM.
        
        Args:
            prompt: The user's prompt/question
            context: Optional context information
            
        Returns:
            Generated response from the LLM
        """
        try:
            # Build the messages for the LLM
            messages = self._build_messages(prompt, context)
            
            # Call the LLM
            response = await self._call_llm(messages)
            
            return response
            
        except Exception as e:
            logger.error(f"Error generating LLM response: {e}")
            return f"I apologize, but I encountered an error while processing your request: {str(e)}"
    
    async def generate_response_stream(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> AsyncGenerator[str, None]:
        """
        Generate a streaming response using the LLM.
        
        Args:
            prompt: The user's prompt/question
            context: Optional context information
            
        Yields:
            Chunks of the generated response
        """
        try:
            # For now, get the full response and simulate streaming
            # TODO: Implement actual streaming when litellm supports it better
            response = await self.generate_response(prompt, context)
            
            # Simulate streaming by yielding words
            words = response.split()
            for i, word in enumerate(words):
                chunk = word + (" " if i < len(words) - 1 else "")
                yield chunk
                await asyncio.sleep(0.05)  # Small delay to simulate streaming
                
        except Exception as e:
            logger.error(f"Error in streaming response: {e}")
            yield f"I apologize, but I encountered an error: {str(e)}"
    
    def _build_messages(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> List[Dict[str, str]]:
        """
        Build the message list for the LLM.
        
        Args:
            prompt: User's prompt
            context: Optional context information
            
        Returns:
            List of messages for the LLM
        """
        messages = []
        
        # System message
        system_prompt = self._build_system_prompt(context)
        messages.append({"role": "system", "content": system_prompt})
        
        # User message
        messages.append({"role": "user", "content": prompt})
        
        return messages
    
    def _build_system_prompt(self, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Build the system prompt based on context.
        
        Args:
            context: Optional context information
            
        Returns:
            System prompt string
        """
        system_parts = [
            "You are an AI coding assistant. You help developers with coding tasks, debugging, and technical questions.",
            "You provide clear, helpful, and accurate responses.",
            "When writing code, include comments and explanations.",
            "If you're unsure about something, say so rather than guessing."
        ]
        
        if context:
            if context.get("project_root"):
                system_parts.append(f"You are working in the project directory: {context['project_root']}")
            
            if context.get("current_file"):
                system_parts.append(f"The current file being worked on is: {context['current_file']}")
            
            if context.get("current_selection"):
                system_parts.append(f"The user has selected this code:\n```\n{context['current_selection']}\n```")
            
            if context.get("symbols_in_scope"):
                symbols = [s.get('name', '') for s in context['symbols_in_scope'][:10]]
                if symbols:
                    system_parts.append(f"Available symbols in scope: {', '.join(symbols)}")
        
        return "\n\n".join(system_parts)
    
    async def _call_llm(self, messages: List[Dict[str, str]]) -> str:
        """
        Call the LLM with the given messages.
        
        Args:
            messages: List of messages for the LLM
            
        Returns:
            Response from the LLM
        """
        try:
            # Use litellm to call the model
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: litellm.completion(
                    model=self.model_name,
                    messages=messages,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens
                )
            )
            
            # Extract the response content
            if response.choices and len(response.choices) > 0:
                content = response.choices[0].message.content
                if content:
                    return content.strip()
            
            return "I apologize, but I didn't receive a proper response from the language model."
            
        except Exception as e:
            logger.error(f"Error calling LLM: {e}")
            raise


# Global LLM client instance
llm_client = LLMClient()
