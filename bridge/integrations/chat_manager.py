"""
Multi-turn chat manager for conversational interface with context preservation.
Integrates with <PERSON><PERSON>E-Agent for code-related queries and maintains conversation history.
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, AsyncGenerator
from dataclasses import dataclass, asdict
from enum import Enum
import threading

from bridge.core.session_manager import session_manager, SessionConfig
from bridge.integrations.swe_agent_interface import SWEAgentInterface
from bridge.core.context_manager import context_analyzer

logger = logging.getLogger(__name__)


class MessageRole(Enum):
    """Message roles in conversation."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class ChatStatus(Enum):
    """Chat session status."""
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class ChatMessage:
    """A single message in a chat conversation."""
    id: str
    role: MessageRole
    content: str
    timestamp: datetime
    context: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        data = asdict(self)
        data['role'] = self.role.value
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class ChatContext:
    """Context information for a chat session."""
    file_path: Optional[str] = None
    project_root: Optional[str] = None
    current_selection: Optional[str] = None
    cursor_position: Optional[Dict[str, int]] = None
    language: Optional[str] = None
    symbols_in_scope: Optional[List[Dict[str, Any]]] = None
    recent_changes: Optional[List[Dict[str, Any]]] = None


@dataclass
class ChatSession:
    """A chat conversation session."""
    session_id: str
    title: str
    messages: List[ChatMessage]
    context: ChatContext
    status: ChatStatus
    created_at: datetime
    updated_at: datetime
    swe_session_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary."""
        data = asdict(self)
        data['status'] = self.status.value
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        data['messages'] = [msg.to_dict() for msg in self.messages]
        return data


class ChatManager:
    """Manages multi-turn chat conversations with context preservation."""
    
    def __init__(self):
        self.sessions: Dict[str, ChatSession] = {}
        self.swe_agent = SWEAgentInterface()
        self.lock = threading.RLock()
        self.active_streams: Dict[str, bool] = {}
    
    def create_session(self, title: str = None, context: ChatContext = None) -> str:
        """
        Create a new chat session.
        
        Args:
            title: Optional title for the session.
            context: Initial context for the session.
            
        Returns:
            Session ID.
        """
        session_id = str(uuid.uuid4())
        
        if title is None:
            title = f"Chat Session {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        if context is None:
            context = ChatContext()
        
        with self.lock:
            session = ChatSession(
                session_id=session_id,
                title=title,
                messages=[],
                context=context,
                status=ChatStatus.ACTIVE,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            self.sessions[session_id] = session
        
        logger.info(f"Created chat session {session_id}: {title}")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Get a chat session by ID."""
        with self.lock:
            return self.sessions.get(session_id)
    
    def list_sessions(self, status: Optional[ChatStatus] = None) -> List[ChatSession]:
        """List all chat sessions, optionally filtered by status."""
        with self.lock:
            sessions = list(self.sessions.values())
        
        if status:
            sessions = [s for s in sessions if s.status == status]
        
        return sorted(sessions, key=lambda s: s.updated_at, reverse=True)
    
    def add_message(self, session_id: str, role: MessageRole, content: str, 
                   context: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        Add a message to a chat session.
        
        Args:
            session_id: Chat session ID.
            role: Message role (user, assistant, system).
            content: Message content.
            context: Optional context information.
            
        Returns:
            Message ID if successful, None otherwise.
        """
        with self.lock:
            session = self.sessions.get(session_id)
            if not session:
                return None
            
            message_id = str(uuid.uuid4())
            message = ChatMessage(
                id=message_id,
                role=role,
                content=content,
                timestamp=datetime.now(),
                context=context
            )
            
            session.messages.append(message)
            session.updated_at = datetime.now()
        
        logger.debug(f"Added {role.value} message to session {session_id}")
        return message_id
    
    def update_context(self, session_id: str, context: ChatContext) -> bool:
        """Update the context for a chat session."""
        with self.lock:
            session = self.sessions.get(session_id)
            if not session:
                return False
            
            session.context = context
            session.updated_at = datetime.now()
        
        return True
    
    async def send_message(self, session_id: str, message: str, 
                          context: Optional[Dict[str, Any]] = None) -> AsyncGenerator[str, None]:
        """
        Send a message and get streaming response.
        
        Args:
            session_id: Chat session ID.
            message: User message.
            context: Optional context information.
            
        Yields:
            Response chunks as they are generated.
        """
        session = self.get_session(session_id)
        if not session:
            yield json.dumps({"error": "Session not found"})
            return
        
        # Add user message
        self.add_message(session_id, MessageRole.USER, message, context)
        
        try:
            # Update context if provided
            if context:
                await self._update_session_context(session_id, context)
            
            # Generate response using SWE-Agent
            response_chunks = []
            async for chunk in self._generate_response(session_id, message):
                response_chunks.append(chunk)
                yield chunk
            
            # Add assistant response
            full_response = "".join(response_chunks)
            self.add_message(session_id, MessageRole.ASSISTANT, full_response)
            
        except Exception as e:
            error_msg = f"Error generating response: {str(e)}"
            logger.error(error_msg)
            self.add_message(session_id, MessageRole.ASSISTANT, error_msg)
            yield json.dumps({"error": error_msg})
    
    async def _update_session_context(self, session_id: str, context: Dict[str, Any]):
        """Update session context with current file and cursor information."""
        session = self.get_session(session_id)
        if not session:
            return
        
        # Extract context information
        file_path = context.get('file_path')
        cursor_line = context.get('cursor_line')
        cursor_column = context.get('cursor_column')
        selection = context.get('selection')
        
        # Analyze current file if provided
        if file_path and context.get('content'):
            try:
                file_context = context_analyzer.analyze_file(
                    file_path, 
                    context['content'], 
                    context.get('language')
                )
                
                # Get symbols in scope
                symbols = []
                if cursor_line is not None:
                    symbols = context_analyzer.get_symbols_in_scope(file_path, cursor_line)
                
                # Update session context
                chat_context = ChatContext(
                    file_path=file_path,
                    project_root=context.get('project_root'),
                    current_selection=selection,
                    cursor_position={'line': cursor_line, 'column': cursor_column} if cursor_line is not None else None,
                    language=file_context.language,
                    symbols_in_scope=[asdict(symbol) for symbol in symbols]
                )
                
                self.update_context(session_id, chat_context)
                
            except Exception as e:
                logger.error(f"Error updating session context: {e}")
    
    async def _generate_response(self, session_id: str, message: str) -> AsyncGenerator[str, None]:
        """Generate response using direct LLM client with conversation context."""
        session = self.get_session(session_id)
        if not session:
            return

        try:
            # Import the LLM client
            from bridge.integrations.llm_client import llm_client

            # Build context-aware prompt
            prompt = self._build_contextual_prompt(session, message)

            # Prepare context for the LLM
            llm_context = {
                "project_root": session.context.project_root,
                "current_file": session.context.current_file,
                "current_selection": session.context.current_selection,
                "symbols_in_scope": session.context.symbols_in_scope
            }

            # Stream response from LLM
            async for chunk in llm_client.generate_response_stream(prompt, llm_context):
                yield chunk

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            yield f"I apologize, but I encountered an error: {str(e)}"
    
    def _build_contextual_prompt(self, session: ChatSession, message: str) -> str:
        """Build a context-aware prompt for SWE-Agent."""
        prompt_parts = []
        
        # Add conversation history
        if session.messages:
            prompt_parts.append("Previous conversation:")
            for msg in session.messages[-5:]:  # Last 5 messages for context
                role_prefix = "User" if msg.role == MessageRole.USER else "Assistant"
                prompt_parts.append(f"{role_prefix}: {msg.content}")
            prompt_parts.append("")
        
        # Add current context
        if session.context.file_path:
            prompt_parts.append(f"Current file: {session.context.file_path}")
        
        if session.context.language:
            prompt_parts.append(f"Language: {session.context.language}")
        
        if session.context.current_selection:
            prompt_parts.append(f"Selected code:\n```\n{session.context.current_selection}\n```")
        
        if session.context.symbols_in_scope:
            symbols = [s.get('name', '') for s in session.context.symbols_in_scope[:10]]
            prompt_parts.append(f"Available symbols: {', '.join(symbols)}")
        
        # Add current message
        prompt_parts.append(f"Current question: {message}")
        
        # Add instructions
        prompt_parts.append("""
Please provide a helpful response considering:
1. The conversation history and context
2. The current file and code selection
3. Available symbols and functions in scope
4. Best practices for the programming language

Be concise but thorough, and provide code examples when helpful.
""")
        
        return "\n".join(prompt_parts)
    
    async def _query_swe_agent(self, prompt: str, context: ChatContext) -> str:
        """Query LLM for chat response using direct LLM client."""
        try:
            # Import the LLM client
            from bridge.integrations.llm_client import llm_client

            # Prepare context for the LLM
            llm_context = {
                "project_root": context.project_root,
                "current_file": context.current_file,
                "current_selection": context.current_selection,
                "symbols_in_scope": context.symbols_in_scope
            }

            # Generate response using the LLM
            response = await llm_client.generate_response(prompt, llm_context)

            return response

        except Exception as e:
            logger.error(f"Error querying LLM: {e}")
            return f"I apologize, but I encountered an error while processing your request: {str(e)}"

    async def _handle_code_generation(self, prompt: str, context: ChatContext) -> str:
        """Handle code generation requests."""
        try:
            # Detect programming language from context or prompt
            language = self._detect_language(prompt, context)

            if "hello world" in prompt.lower() or "helloworld" in prompt.lower():
                return self._generate_hello_world(language)

            # For other code generation, provide a template
            return f"I can help you generate {language} code. Here's a basic template:\n\n```{language}\n// Your code here\n```\n\nCould you provide more specific requirements?"

        except Exception as e:
            logger.error(f"Error in code generation: {e}")
            return f"I encountered an error while generating code: {str(e)}"

    def _detect_language(self, prompt: str, context: ChatContext) -> str:
        """Detect programming language from prompt and context."""
        # Check context first
        if context and context.language:
            return context.language

        # Check prompt for language keywords
        prompt_lower = prompt.lower()
        if "java" in prompt_lower:
            return "java"
        elif "python" in prompt_lower:
            return "python"
        elif "javascript" in prompt_lower or "js" in prompt_lower:
            return "javascript"
        elif "typescript" in prompt_lower or "ts" in prompt_lower:
            return "typescript"
        elif "c++" in prompt_lower or "cpp" in prompt_lower:
            return "cpp"
        elif "c#" in prompt_lower or "csharp" in prompt_lower:
            return "csharp"
        elif "go" in prompt_lower and "golang" in prompt_lower:
            return "go"
        elif "rust" in prompt_lower:
            return "rust"

        # Default to Java if no language detected
        return "java"

    def _generate_hello_world(self, language: str) -> str:
        """Generate Hello World program for the specified language."""
        templates = {
            "java": '''Here's a Hello World program in Java:

```java
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}
```

This is a basic Java program that:
1. Defines a public class named `HelloWorld`
2. Contains a `main` method as the entry point
3. Prints "Hello, World!" to the console

To run this program:
1. Save it as `HelloWorld.java`
2. Compile with: `javac HelloWorld.java`
3. Run with: `java HelloWorld`''',

            "python": '''Here's a Hello World program in Python:

```python
def main():
    print("Hello, World!")

if __name__ == "__main__":
    main()
```

This Python program:
1. Defines a `main` function that prints "Hello, World!"
2. Uses the `if __name__ == "__main__":` guard to run the main function
3. Can be executed directly with: `python hello_world.py`''',

            "javascript": '''Here's a Hello World program in JavaScript:

```javascript
function main() {
    console.log("Hello, World!");
}

main();
```

This JavaScript program:
1. Defines a function that logs "Hello, World!" to the console
2. Calls the function immediately
3. Can be run with Node.js: `node hello_world.js`''',

            "typescript": '''Here's a Hello World program in TypeScript:

```typescript
function main(): void {
    console.log("Hello, World!");
}

main();
```

This TypeScript program:
1. Defines a typed function that logs "Hello, World!"
2. Uses explicit return type annotation
3. Compile with: `tsc hello_world.ts` then run: `node hello_world.js`''',
        }

        return templates.get(language, templates["java"])
    
    def delete_session(self, session_id: str) -> bool:
        """Delete a chat session."""
        with self.lock:
            if session_id in self.sessions:
                del self.sessions[session_id]
                logger.info(f"Deleted chat session {session_id}")
                return True
        return False
    
    def clear_session_messages(self, session_id: str) -> bool:
        """Clear all messages from a chat session."""
        with self.lock:
            session = self.sessions.get(session_id)
            if session:
                session.messages.clear()
                session.updated_at = datetime.now()
                return True
        return False
    
    def get_session_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get a summary of a chat session."""
        session = self.get_session(session_id)
        if not session:
            return None
        
        return {
            "session_id": session.session_id,
            "title": session.title,
            "status": session.status.value,
            "message_count": len(session.messages),
            "created_at": session.created_at.isoformat(),
            "updated_at": session.updated_at.isoformat(),
            "context": asdict(session.context) if session.context else None
        }


# Global chat manager instance
chat_manager = ChatManager()
